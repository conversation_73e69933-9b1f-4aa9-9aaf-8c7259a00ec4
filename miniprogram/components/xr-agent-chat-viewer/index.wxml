<view class="chat-assistant">
  <!-- 输入框和发送按钮在聊天窗口上方 -->
  <view class="input-container">
    <xr-agent-chat-sr
    isLoadingRaw="{{isLoading}}"
    bindonSrStart="handleSrStart"
    bindonSrInput="handleSrInput"
    ></xr-agent-chat-sr>
    <input id="input-box" class="message-input" placeholder="输入消息..." bindinput="onInput" value="{{inputText}}"/>
    <button class="send-button" bindtap="sendMessage" disabled="{{isLoading}}" style="width: 68rpx; display: block; box-sizing: border-box; left: 0rpx; top: 0rpx">
      <image src="{{isLoading ? '/assets/image/send-message-disable.png' : '/assets/image/send-message.png'}}" class="send-icon {{isLoading ? 'disable' : ''}}" />
    </button>
  </view>

  <!-- 消息窗口 -->
  <scroll-view scroll-top="{{scrollTop}}" scroll-with-animation="true" class="messages-window" id="messages-window" scroll-y="true">
    <view class="scroll-content" id="scroll-content">
      <view wx:for="{{messages}}" wx:key="index" class="message-item">
      <!-- 头像 -->
      <image src="{{item.avatarUrl}}" class="avatar" />
      <!-- 消息内容 -->
      <text user-select class="{{item.isUser ? 'user-message' : 'assistant-message'}}">{{item.content}}</text>
    </view>
    </view>
  </scroll-view>
  <xr-bottom-debug-info 
    debugItemListRaw="{{debugItemList}}"
  />
</view>
